'use client';

import React, { createContext, useContext, useState, useEffect, useCallback } from 'react';
import { useRouter } from 'next/navigation';
import { toast } from 'sonner';

interface StaffUser {
  id: number;
  username: string;
  route_code: string;
}

interface StaffContextType {
  staff: StaffUser | null;
  isLoading: boolean;
  isAuthenticated: boolean;
  login: (staffData: StaffUser) => void;
  logout: () => Promise<void>;
  validateSession: () => Promise<boolean>;
}

const StaffContext = createContext<StaffContextType | undefined>(undefined);

export function StaffProvider({ children }: { children: React.ReactNode }) {
  const [staff, setStaff] = useState<StaffUser | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const router = useRouter();

  const isAuthenticated = !!staff;

  const validateSession = useCallback(async (): Promise<boolean> => {
    try {
      const response = await fetch('/api/staff/validate', {
        credentials: 'include'
      });

      if (response.ok) {
        const result = await response.json();
        if (result.success && result.data.valid) {
          setStaff(result.data.staff);
          setIsLoading(false);
          return true;
        }
      }

      // Session invalid, clear staff data
      setStaff(null);
      localStorage.removeItem('staff_user');
      setIsLoading(false);
      return false;
    } catch (error) {
      console.error('Staff session validation error:', error);
      setStaff(null);
      localStorage.removeItem('staff_user');
      setIsLoading(false);
      return false;
    }
  }, []);

  useEffect(() => {
    // Check for stored staff data
    const storedStaff = localStorage.getItem('staff_user');
    if (storedStaff) {
      try {
        const staffData = JSON.parse(storedStaff);
        setStaff(staffData);
      } catch (error) {
        localStorage.removeItem('staff_user');
      }
    }

    // Validate session on mount
    validateSession();
  }, [validateSession]);

  const login = useCallback((staffData: StaffUser) => {
    setStaff(staffData);
    localStorage.setItem('staff_user', JSON.stringify(staffData));
    setIsLoading(false);
  }, []);

  const logout = useCallback(async () => {
    try {
      setIsLoading(true);

      // Call logout API
      await fetch('/api/staff/logout', {
        method: 'POST',
        credentials: 'include'
      });

      // Clear staff data
      setStaff(null);
      localStorage.removeItem('staff_user');

      toast.success('Logged out successfully');
      router.push('/staff/login');
    } catch (error) {
      console.error('Staff logout error:', error);
      toast.error('Logout failed');
    } finally {
      setIsLoading(false);
    }
  }, [router]);

  const value: StaffContextType = {
    staff,
    isLoading,
    isAuthenticated,
    login,
    logout,
    validateSession
  };

  return (
    <StaffContext.Provider value={value}>
      {children}
    </StaffContext.Provider>
  );
}

export function useStaff() {
  const context = useContext(StaffContext);
  if (context === undefined) {
    throw new Error('useStaff must be used within a StaffProvider');
  }
  return context;
}

// Higher-order component for protecting staff routes
export function withStaffAuth<P extends object>(
  Component: React.ComponentType<P>
) {
  return function AuthenticatedComponent(props: P) {
    const { isAuthenticated, isLoading } = useStaff();
    const router = useRouter();

    useEffect(() => {
      if (!isLoading && !isAuthenticated) {
        router.push('/staff/login');
      }
    }, [isAuthenticated, isLoading, router]);

    if (isLoading) {
      return (
        <div className="min-h-screen flex items-center justify-center">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
        </div>
      );
    }

    if (!isAuthenticated) {
      return null;
    }

    return <Component {...props} />;
  };
}

'use client';

import React, { useState, useEffect, useCallback } from 'react';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Download, TrendingUp, Users, Bus, Target } from 'lucide-react';
import { toast } from 'sonner';

interface DateRange {
  startDate: string;
  endDate: string;
}

interface RouteInfo {
  routeCode: string;
  busName: string;
  totalBookings: number;
  capacity: number;
  utilizationPercentage: number;
  demandLevel: 'High' | 'Medium' | 'Low';
  availableSeats: number;
  isActive: boolean;
}

interface DailyTrend {
  date: string;
  totalBookings: number;
  routeBreakdown: { [route: string]: number };
}

interface RouteDemandData {
  routes: RouteInfo[];
  dailyTrends: DailyTrend[];
  summary: {
    dateRange: {
      startDate: string;
      endDate: string;
    };
    totalRoutes: number;
    totalBookings: number;
    totalCapacity: number;
    overallUtilization: number;
    demandDistribution: {
      high: number;
      medium: number;
      low: number;
    };
    highestDemandRoute: RouteInfo | null;
    lowestDemandRoute: RouteInfo | null;
  };
}

interface RouteDemandTabProps {
  dateRange: DateRange;
  isRefreshing: boolean;
}

export default function RouteDemandTab({ dateRange, isRefreshing }: RouteDemandTabProps) {
  const [data, setData] = useState<RouteDemandData | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchRouteDemandData = useCallback(async () => {
    setIsLoading(true);
    setError(null);

    try {
      const params = new URLSearchParams({
        start_date: dateRange.startDate,
        end_date: dateRange.endDate
      });

      const response = await fetch(`/api/admin/custom-reports/route-demand?${params}`, {
        credentials: 'include'
      });

      const result = await response.json();

      if (result.success) {
        setData(result.data);
      } else {
        setError(result.error || 'Failed to fetch route demand data');
        toast.error('Failed to fetch route demand data');
      }
    } catch (error) {
      setError('Network error while fetching route demand data');
      toast.error('Network error while fetching route demand data');
      console.error('Route demand fetch error:', error);
    } finally {
      setIsLoading(false);
    }
  }, [dateRange]);

  useEffect(() => {
    fetchRouteDemandData();
  }, [fetchRouteDemandData]);

  const handleExport = () => {
    if (!data) return;
    
    const headers = ['Route Code', 'Bus Name', 'Total Bookings', 'Capacity', 'Utilization %', 'Demand Level', 'Available Seats'];
    const csvContent = [
      headers.join(','),
      ...data.routes.map(route => [
        route.routeCode,
        `"${route.busName}"`,
        route.totalBookings,
        route.capacity,
        route.utilizationPercentage.toFixed(2) + '%',
        route.demandLevel,
        route.availableSeats
      ].join(','))
    ].join('\n');

    const blob = new Blob([csvContent], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `route-demand-${dateRange.startDate}-to-${dateRange.endDate}.csv`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    window.URL.revokeObjectURL(url);
    
    toast.success('Route demand data exported successfully');
  };

  const getDemandBadgeColor = (level: 'High' | 'Medium' | 'Low') => {
    switch (level) {
      case 'High':
        return 'bg-red-100 text-red-800 border-red-200';
      case 'Medium':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'Low':
        return 'bg-green-100 text-green-800 border-green-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center py-12">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading route demand data...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="text-center py-12">
        <p className="text-red-600 mb-4">{error}</p>
        <Button onClick={fetchRouteDemandData} variant="outline">
          Try Again
        </Button>
      </div>
    );
  }

  if (!data) {
    return (
      <div className="text-center py-12">
        <p className="text-gray-600">No route demand data available</p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <div>
                <p className="text-sm font-medium text-gray-600">Total Routes</p>
                <p className="text-2xl font-bold text-blue-600">{data.summary.totalRoutes}</p>
              </div>
              <Bus className="h-8 w-8 text-blue-600 ml-auto" />
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <div>
                <p className="text-sm font-medium text-gray-600">Total Bookings</p>
                <p className="text-2xl font-bold text-green-600">{data.summary.totalBookings}</p>
              </div>
              <Users className="h-8 w-8 text-green-600 ml-auto" />
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <div>
                <p className="text-sm font-medium text-gray-600">Total Capacity</p>
                <p className="text-2xl font-bold text-purple-600">{data.summary.totalCapacity}</p>
              </div>
              <Target className="h-8 w-8 text-purple-600 ml-auto" />
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <div>
                <p className="text-sm font-medium text-gray-600">Overall Utilization</p>
                <p className="text-2xl font-bold text-orange-600">{data.summary.overallUtilization}%</p>
              </div>
              <TrendingUp className="h-8 w-8 text-orange-600 ml-auto" />
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-6">
            <div className="text-center">
              <p className="text-sm font-medium text-gray-600 mb-2">Demand Distribution</p>
              <div className="space-y-1">
                <div className="flex justify-between text-xs">
                  <span className="text-red-600">High: {data.summary.demandDistribution.high}</span>
                  <span className="text-yellow-600">Med: {data.summary.demandDistribution.medium}</span>
                  <span className="text-green-600">Low: {data.summary.demandDistribution.low}</span>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Top and Bottom Performing Routes */}
      {data.summary.highestDemandRoute && (
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <Card>
            <CardHeader>
              <CardTitle className="text-red-600">Highest Demand Route</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                <p className="font-semibold">{data.summary.highestDemandRoute.busName}</p>
                <p className="text-sm text-gray-600">Route: {data.summary.highestDemandRoute.routeCode}</p>
                <p className="text-lg font-bold text-red-600">{data.summary.highestDemandRoute.totalBookings} bookings</p>
                <p className="text-sm text-gray-600">Utilization: {data.summary.highestDemandRoute.utilizationPercentage}%</p>
                <Badge className={getDemandBadgeColor(data.summary.highestDemandRoute.demandLevel)}>
                  {data.summary.highestDemandRoute.demandLevel} Demand
                </Badge>
              </div>
            </CardContent>
          </Card>
          {data.summary.lowestDemandRoute && (
            <Card>
              <CardHeader>
                <CardTitle className="text-green-600">Lowest Demand Route</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  <p className="font-semibold">{data.summary.lowestDemandRoute.busName}</p>
                  <p className="text-sm text-gray-600">Route: {data.summary.lowestDemandRoute.routeCode}</p>
                  <p className="text-lg font-bold text-green-600">{data.summary.lowestDemandRoute.totalBookings} bookings</p>
                  <p className="text-sm text-gray-600">Utilization: {data.summary.lowestDemandRoute.utilizationPercentage}%</p>
                  <Badge className={getDemandBadgeColor(data.summary.lowestDemandRoute.demandLevel)}>
                    {data.summary.lowestDemandRoute.demandLevel} Demand
                  </Badge>
                </div>
              </CardContent>
            </Card>
          )}
        </div>
      )}

      {/* Route Demand Table */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            Route Demand Analysis
            <Button onClick={handleExport} variant="outline" size="sm">
              <Download className="w-4 h-4 mr-2" />
              Export Data
            </Button>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="overflow-x-auto">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Route</TableHead>
                  <TableHead>Bus Name</TableHead>
                  <TableHead>Bookings</TableHead>
                  <TableHead>Capacity</TableHead>
                  <TableHead>Utilization</TableHead>
                  <TableHead>Available Seats</TableHead>
                  <TableHead>Demand Level</TableHead>
                  <TableHead>Performance</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {data.routes.length > 0 ? (
                  data.routes.map((route) => (
                    <TableRow key={route.routeCode}>
                      <TableCell className="font-medium">{route.routeCode}</TableCell>
                      <TableCell>{route.busName}</TableCell>
                      <TableCell className="font-semibold">{route.totalBookings}</TableCell>
                      <TableCell>{route.capacity}</TableCell>
                      <TableCell>{route.utilizationPercentage.toFixed(1)}%</TableCell>
                      <TableCell>{route.availableSeats}</TableCell>
                      <TableCell>
                        <Badge className={getDemandBadgeColor(route.demandLevel)}>
                          {route.demandLevel}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <div className="w-full">
                          <Progress 
                            value={route.utilizationPercentage} 
                            className="h-2"
                          />
                        </div>
                      </TableCell>
                    </TableRow>
                  ))
                ) : (
                  <TableRow>
                    <TableCell colSpan={8} className="text-center py-8 text-gray-500">
                      No route demand data found for the selected date range
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
          </div>
        </CardContent>
      </Card>

      {/* Daily Trends */}
      <Card>
        <CardHeader>
          <CardTitle>Daily Booking Trends</CardTitle>
        </CardHeader>
        <CardContent>
          {data.dailyTrends.length > 0 ? (
            <div className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {data.dailyTrends.map((day) => (
                  <div key={day.date} className="p-4 border rounded-lg">
                    <div className="flex justify-between items-center mb-2">
                      <span className="text-sm font-medium text-gray-600">
                        {new Date(day.date).toLocaleDateString()}
                      </span>
                      <span className="text-lg font-bold text-blue-600">
                        {day.totalBookings} bookings
                      </span>
                    </div>
                    <div className="text-xs text-gray-500">
                      {Object.keys(day.routeBreakdown).length} routes active
                    </div>
                  </div>
                ))}
              </div>
            </div>
          ) : (
            <div className="text-center py-8 text-gray-500">
              No daily trend data available for the selected date range
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}

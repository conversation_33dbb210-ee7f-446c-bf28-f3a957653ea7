'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import { PageTransition } from '@/components/ui/page-transition';
import { useStaff, withStaffAuth } from '@/contexts/StaffContext';
import { ArrowLeft, Crown, Users, Bus, Calendar, RefreshCw } from 'lucide-react';
import { motion } from 'framer-motion';
import { toast } from 'sonner';

interface HistoricalBooking {
  id: number;
  admission_number: string;
  student_name: string;
  bus_route: string;
  destination: string;
  created_at: string;
  go_date: string | null;
  return_date: string | null;
  is_special: boolean | null;
}

function HistoricalBookingsPage() {
  const { staff } = useStaff();
  const [bookings, setBookings] = useState<HistoricalBooking[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const router = useRouter();

  const fetchHistoricalBookings = async () => {
    try {
      setIsLoading(true);
      const response = await fetch(`/api/staff/historical-bookings?route=${staff?.route_code}`, {
        credentials: 'include'
      });

      if (response.ok) {
        const result = await response.json();
        if (result.success) {
          // Fix: Access the nested data structure correctly
          setBookings(result.data.bookings || []);
        } else {
          toast.error('Failed to fetch historical bookings');
        }
      } else {
        toast.error('Failed to fetch historical bookings');
      }
    } catch (error) {
      console.error('Error fetching historical bookings:', error);
      toast.error('Failed to fetch historical bookings');
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    if (staff?.route_code) {
      fetchHistoricalBookings();
    }
  }, [staff?.route_code]);

  const handleBackToDashboard = () => {
    router.push('/staff/dashboard');
  };

  const formatDate = (dateString: string | null) => {
    if (!dateString) return 'N/A';
    return new Date(dateString).toLocaleDateString('en-IN', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  const formatDateTime = (dateString: string) => {
    return new Date(dateString).toLocaleString('en-IN', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  return (
    <PageTransition>
      <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-green-50">
        {/* Header */}
        <div className="bg-white shadow-sm border-b">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="flex justify-between items-center py-4">
              <div className="flex items-center space-x-4">
                <Button
                  onClick={handleBackToDashboard}
                  variant="outline"
                  size="sm"
                  className="mr-4"
                >
                  <ArrowLeft className="w-4 h-4 mr-2" />
                  Back to Dashboard
                </Button>
                <div className="w-10 h-10 bg-gradient-to-r from-blue-600 to-green-600 rounded-full flex items-center justify-center">
                  <Users className="w-6 h-6 text-white" />
                </div>
                <div>
                  <h1 className="text-2xl font-bold text-gray-900">All Time Bookings</h1>
                  <p className="text-gray-600">Historical data for {staff?.username}</p>
                </div>
              </div>
              <div className="flex items-center space-x-4">
                <Badge variant="outline" className="px-3 py-1">
                  <Bus className="w-4 h-4 mr-2" />
                  Route: {staff?.route_code}
                </Badge>
                <Button
                  onClick={fetchHistoricalBookings}
                  variant="outline"
                  size="sm"
                  disabled={isLoading}
                >
                  <RefreshCw className={`w-4 h-4 mr-2 ${isLoading ? 'animate-spin' : ''}`} />
                  Refresh
                </Button>
              </div>
            </div>
          </div>
        </div>

        {/* Main Content */}
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          {/* Stats Card */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.1 }}
            className="mb-8"
          >
            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Total Historical Bookings</p>
                    <p className="text-3xl font-bold text-gray-900">{bookings.length}</p>
                  </div>
                  <Users className="w-8 h-8 text-blue-600" />
                </div>
              </CardContent>
            </Card>
          </motion.div>

          {/* Historical Bookings Table */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.2 }}
          >
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Calendar className="w-5 h-5" />
                  All Time Bookings - {staff?.route_code}
                </CardTitle>
              </CardHeader>
              <CardContent>
                {isLoading ? (
                  <div className="flex items-center justify-center py-8">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
                  </div>
                ) : bookings.length === 0 ? (
                  <div className="text-center py-8 text-gray-500">
                    No historical bookings found for this route.
                  </div>
                ) : (
                  <div className="overflow-x-auto">
                    <Table>
                      <TableHeader>
                        <TableRow>
                          <TableHead>Student Name</TableHead>
                          <TableHead>Admission No.</TableHead>
                          <TableHead>Booking Date & Time</TableHead>
                          <TableHead>Destination</TableHead>
                          <TableHead>Go Date</TableHead>
                          <TableHead>Return Date</TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {bookings.map((booking) => (
                          <TableRow key={booking.id}>
                            <TableCell className="font-medium">
                              <div className="flex items-center gap-2">
                                {booking.is_special && (
                                  <Crown className="w-4 h-4 text-yellow-500" title="Special Pass" />
                                )}
                                {booking.student_name}
                              </div>
                            </TableCell>
                            <TableCell>{booking.admission_number}</TableCell>
                            <TableCell>{formatDateTime(booking.created_at)}</TableCell>
                            <TableCell>{booking.destination}</TableCell>
                            <TableCell>{formatDate(booking.go_date)}</TableCell>
                            <TableCell>{formatDate(booking.return_date)}</TableCell>
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                  </div>
                )}
              </CardContent>
            </Card>
          </motion.div>
        </div>
      </div>
    </PageTransition>
  );
}

export default withStaffAuth(HistoricalBookingsPage); 
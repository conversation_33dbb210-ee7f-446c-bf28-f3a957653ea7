export const dynamic = "force-dynamic";

import { NextResponse } from 'next/server';
import { authenticateStaff, createStaffSession, cleanExpiredStaffSessions } from '@/lib/auth';
import { validateRequestBody, createApiResponse, handleApiError, setStaffAuthCookie } from '@/lib/middleware';

export async function POST(request: Request) {
  try {
    const body = await request.json();

    // Validate request body
    const validation = validateRequestBody<{ username: string; password: string }>(
      body,
      ['username', 'password']
    );

    if (!validation.isValid) {
      return NextResponse.json(
        { error: 'Validation failed', details: validation.errors },
        { status: 400 }
      );
    }

    const { username, password } = body;

    // Clean expired sessions periodically
    await cleanExpiredStaffSessions();

    // Authenticate staff user
    const staff = await authenticateStaff(username, password);
    if (!staff) {
      return NextResponse.json(
        { error: 'Invalid credentials' },
        { status: 401 }
      );
    }

    // Create session
    const sessionResult = await createStaffSession(staff);
    if (!sessionResult) {
      return NextResponse.json(
        { error: 'Failed to create session' },
        { status: 500 }
      );
    }

    // Create response with staff data (excluding sensitive info)
    const response = createApiResponse({
      staff: {
        id: staff.id,
        username: staff.username,
        route_code: staff.route_code
      },
      token: sessionResult.token
    });

    // Set secure cookie
    setStaffAuthCookie(response, sessionResult.token);

    return response;
  } catch (error) {
    return handleApiError(error, 'Staff login failed');
  }
}

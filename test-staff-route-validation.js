// Test script to verify staff route validation functionality
const fetch = require('node-fetch');

async function testStaffRouteValidation() {
  console.log('Testing Staff Route Validation...\n');

  try {
    // Test 1: Get route assignments
    console.log('1. Testing GET /api/admin/staff/route-assignments...');
    const assignmentsResponse = await fetch('http://localhost:3000/api/admin/staff/route-assignments', {
      method: 'GET',
      headers: {
        'Cookie': 'admin_token=your_admin_token_here' // Replace with actual token
      }
    });

    console.log('Assignments Response Status:', assignmentsResponse.status);
    
    if (assignmentsResponse.ok) {
      const assignmentsData = await assignmentsResponse.json();
      console.log('Assignments Response Data:', JSON.stringify(assignmentsData, null, 2));
      console.log('Available Routes:', assignmentsData.data?.filter(r => !r.is_assigned).length || 0);
      console.log('Assigned Routes:', assignmentsData.data?.filter(r => r.is_assigned).length || 0);
    } else {
      console.log('Assignments Response Error:', assignmentsResponse.statusText);
    }

    console.log('\n' + '='.repeat(50) + '\n');

    // Test 2: Try to create staff with already assigned route
    console.log('2. Testing POST /api/admin/staff with occupied route...');
    const createResponse = await fetch('http://localhost:3000/api/admin/staff', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Cookie': 'admin_token=your_admin_token_here' // Replace with actual token
      },
      body: JSON.stringify({
        username: 'test_staff_duplicate_' + Date.now(),
        password: 'testpassword123',
        route_code: 'route-1' // Assuming this route is already assigned
      })
    });

    console.log('Create Response Status:', createResponse.status);
    
    if (createResponse.ok) {
      const createData = await createResponse.json();
      console.log('Create Response Data:', JSON.stringify(createData, null, 2));
    } else {
      const createError = await createResponse.json();
      console.log('Create Response Error:', JSON.stringify(createError, null, 2));
    }

    console.log('\n' + '='.repeat(50) + '\n');

    // Test 3: Try to create staff with available route
    console.log('3. Testing POST /api/admin/staff with available route...');
    const createAvailableResponse = await fetch('http://localhost:3000/api/admin/staff', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Cookie': 'admin_token=your_admin_token_here' // Replace with actual token
      },
      body: JSON.stringify({
        username: 'test_staff_available_' + Date.now(),
        password: 'testpassword123',
        route_code: 'route-4' // Assuming this route is available
      })
    });

    console.log('Create Available Response Status:', createAvailableResponse.status);
    
    if (createAvailableResponse.ok) {
      const createAvailableData = await createAvailableResponse.json();
      console.log('Create Available Response Data:', JSON.stringify(createAvailableData, null, 2));
    } else {
      const createAvailableError = await createAvailableResponse.json();
      console.log('Create Available Response Error:', JSON.stringify(createAvailableError, null, 2));
    }

  } catch (error) {
    console.error('Test failed:', error);
  }
}

// Run the test
testStaffRouteValidation(); 
const { createClient } = require('@supabase/supabase-js');

// Test the custom reports bookings API endpoint
async function testCustomReportsBookings() {
  console.log('Testing Custom Reports Bookings API endpoint...\n');

  try {
    // Test with date range
    const startDate = '2025-01-01';
    const endDate = '2025-01-31';
    
    const url = `http://localhost:3000/api/admin/custom-reports/bookings?start_date=${startDate}&end_date=${endDate}&page=1&limit=10`;
    
    console.log('Testing URL:', url);
    
    const response = await fetch(url, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        // Note: In a real test, you'd need proper authentication
        // This is just for testing the data structure
      }
    });

    if (!response.ok) {
      console.log('Response status:', response.status);
      console.log('Response status text:', response.statusText);
      
      if (response.status === 401) {
        console.log('Authentication required - this is expected for admin endpoints');
        console.log('Testing data structure with mock data instead...\n');
        
        // Test the validation logic with mock data
        testValidationLogic();
        return;
      }
      
      const errorText = await response.text();
      console.log('Error response:', errorText);
      return;
    }

    const data = await response.json();
    
    console.log('Response received successfully!');
    console.log('Response structure:', JSON.stringify(data, null, 2));
    
    // Validate the availableRoutes array
    if (data.success && data.data && data.data.availableRoutes) {
      const availableRoutes = data.data.availableRoutes;
      console.log('\nValidating availableRoutes array...');
      console.log('Available routes count:', availableRoutes.length);
      console.log('Available routes:', availableRoutes);
      
      // Check for empty strings or invalid values
      const invalidRoutes = availableRoutes.filter(route => 
        !route || 
        typeof route !== 'string' || 
        route.trim() === '' || 
        route === 'undefined' || 
        route === 'null' || 
        route === 'None'
      );
      
      if (invalidRoutes.length > 0) {
        console.error('❌ Found invalid routes in availableRoutes:', invalidRoutes);
      } else {
        console.log('✅ All routes in availableRoutes are valid');
      }
      
      // Check for duplicates
      const uniqueRoutes = new Set(availableRoutes);
      if (uniqueRoutes.size !== availableRoutes.length) {
        console.error('❌ Found duplicate routes in availableRoutes');
      } else {
        console.log('✅ No duplicate routes found');
      }
    } else {
      console.error('❌ Response structure is invalid or missing availableRoutes');
    }
    
  } catch (error) {
    console.error('❌ Test failed with error:', error.message);
  }
}

// Test the validation logic with mock data
function testValidationLogic() {
  console.log('Testing validation logic with mock data...\n');
  
  // Mock the isValidRoute function
  function isValidRoute(route) {
    return route && 
           typeof route === 'string' && 
           route.trim() !== '' && 
           route !== 'undefined' && 
           route !== 'null' && 
           route !== 'None' &&
           route.trim().length > 0;
  }
  
  // Test cases
  const testCases = [
    'Route A',
    'Route B',
    '',
    '   ',
    null,
    undefined,
    'undefined',
    'null',
    'None',
    'Route C',
    123,
    {},
    []
  ];
  
  console.log('Test cases:', testCases);
  console.log('\nValidation results:');
  
  testCases.forEach((testCase, index) => {
    const isValid = isValidRoute(testCase);
    const status = isValid ? '✅' : '❌';
    console.log(`${status} Test case ${index + 1}: ${JSON.stringify(testCase)} -> ${isValid}`);
  });
  
  // Test filtering
  const mockBookings = [
    { bus_route: 'Route A' },
    { bus_route: 'Route B' },
    { bus_route: '' },
    { bus_route: '   ' },
    { bus_route: null },
    { bus_route: undefined },
    { bus_route: 'undefined' },
    { bus_route: 'null' },
    { bus_route: 'None' },
    { bus_route: 'Route C' }
  ];
  
  const validRoutes = Array.from(
    new Set(
      mockBookings
        .map(b => b.bus_route)
        .filter(isValidRoute)
    )
  ).sort();
  
  console.log('\nMock bookings filtering:');
  console.log('Original routes:', mockBookings.map(b => b.bus_route));
  console.log('Valid routes after filtering:', validRoutes);
  
  // Final validation
  const cleanAvailableRoutes = validRoutes.filter(route => 
    route && 
    typeof route === 'string' && 
    route.trim() !== '' && 
    route.length > 0
  );
  
  console.log('Final clean available routes:', cleanAvailableRoutes);
  
  // Check for any invalid values
  const invalidInFinal = cleanAvailableRoutes.filter(route => 
    !route || 
    typeof route !== 'string' || 
    route.trim() === '' || 
    route === 'undefined' || 
    route === 'null' || 
    route === 'None'
  );
  
  if (invalidInFinal.length > 0) {
    console.error('❌ Found invalid routes in final array:', invalidInFinal);
  } else {
    console.log('✅ Final validation passed - no invalid routes found');
  }
}

// Run the test
testCustomReportsBookings(); 
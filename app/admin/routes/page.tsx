'use client';

import { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { PageTransition } from '@/components/ui/page-transition';
import { useAdmin, withAdminAuth } from '@/contexts/AdminContext';
import { Route, Plus, Edit, Trash2, Save, X, ArrowLeft, MapPin, IndianRupee, AlertCircle } from 'lucide-react';
import { motion, AnimatePresence } from 'framer-motion';
import { toast } from 'sonner';
import Link from 'next/link';

interface Bus {
  id: number;
  name: string;
  route_code: string;
  is_active: boolean;
}

interface RouteStop {
  id?: number;
  route_code: string;
  stop_name: string;
  fare: number;
  stop_order: number;
  is_active: boolean;
  created_at?: string;
  updated_at?: string;
}

interface FormData {
  route_code: string;
  stop_name: string;
  fare: number;
  stop_order: number;
  is_active: boolean;
}

interface ValidationErrors {
  stop_name?: string;
  fare?: string;
  stop_order?: string;
}

function RouteManagement() {
  const { user } = useAdmin();
  const [buses, setBuses] = useState<Bus[]>([]);
  const [routeStops, setRouteStops] = useState<RouteStop[]>([]);
  const [selectedBus, setSelectedBus] = useState<string>('');
  const [isLoading, setIsLoading] = useState(true);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [isEditMode, setIsEditMode] = useState(false);
  const [currentStop, setCurrentStop] = useState<RouteStop | null>(null);
  const [formData, setFormData] = useState<FormData>({
    route_code: '',
    stop_name: '',
    fare: 0,
    stop_order: 1,
    is_active: true
  });
  const [errors, setErrors] = useState<ValidationErrors>({});

  useEffect(() => {
    fetchBuses();
  }, []);

  useEffect(() => {
    if (selectedBus) {
      fetchRouteStops(selectedBus);
    } else {
      setRouteStops([]);
    }
  }, [selectedBus]);

  const fetchBuses = async () => {
    try {
      const response = await fetch('/api/admin/buses');
      if (response.ok) {
        const result = await response.json();
        if (result.success) {
          setBuses(result.data);
        }
      }
    } catch (error) {
      toast.error('Failed to fetch buses');
    } finally {
      setIsLoading(false);
    }
  };

  const fetchRouteStops = async (routeCode: string) => {
    try {
      const response = await fetch(`/api/admin/route-stops?route_code=${routeCode}`);
      if (response.ok) {
        const result = await response.json();
        if (result.success) {
          setRouteStops(result.data.sort((a: RouteStop, b: RouteStop) => a.stop_order - b.stop_order));
        }
      }
    } catch (error) {
      toast.error('Failed to fetch route stops');
    }
  };

  const validateForm = (): boolean => {
    const newErrors: ValidationErrors = {};

    if (!formData.stop_name.trim()) {
      newErrors.stop_name = 'Stop name is required';
    }

    if (formData.fare <= 0) {
      newErrors.fare = 'Fare must be greater than 0';
    }

    if (formData.stop_order < 1) {
      newErrors.stop_order = 'Stop order must be at least 1';
    }

    // Check for duplicate stop order
    const existingStopOrder = routeStops.find(stop => 
      stop.stop_order === formData.stop_order && 
      (!isEditMode || stop.id !== currentStop?.id)
    );
    if (existingStopOrder) {
      newErrors.stop_order = 'Stop order already exists';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const openCreateModal = () => {
    setIsEditMode(false);
    setCurrentStop(null);
    setFormData({
      route_code: selectedBus,
      stop_name: '',
      fare: 1,
      stop_order: getNextStopOrder(),
      is_active: true
    });
    setErrors({});
    setIsModalOpen(true);
  };

  const openEditModal = (stop: RouteStop) => {
    setIsEditMode(true);
    setCurrentStop(stop);
    setFormData({
      route_code: stop.route_code,
      stop_name: stop.stop_name,
      fare: stop.fare,
      stop_order: stop.stop_order,
      is_active: stop.is_active
    });
    setErrors({});
    setIsModalOpen(true);
  };

  const closeModal = () => {
    setIsModalOpen(false);
    setCurrentStop(null);
    setFormData({
      route_code: '',
      stop_name: '',
      fare: 0,
      stop_order: 1,
      is_active: true
    });
    setErrors({});
  };

  const handleSubmit = async () => {
    if (!validateForm()) {
      return;
    }

    try {
      const url = '/api/admin/route-stops';
      const method = isEditMode ? 'PUT' : 'POST';
      const body = isEditMode ? { ...formData, id: currentStop?.id } : formData;

      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(body),
        credentials: 'include'
      });

      const result = await response.json();

      if (response.ok && result.success) {
        toast.success(isEditMode ? 'Route stop updated successfully' : 'Route stop created successfully');
        fetchRouteStops(selectedBus);
        closeModal();
      } else {
        toast.error(result.error || `Failed to ${isEditMode ? 'update' : 'create'} route stop`);
      }
    } catch (error) {
      toast.error(`Failed to ${isEditMode ? 'update' : 'create'} route stop`);
    }
  };

  const handleDelete = async (id: number) => {
    if (!confirm('Are you sure you want to delete this route stop?')) {
      return;
    }

    try {
      const response = await fetch(`/api/admin/route-stops?id=${id}`, {
        method: 'DELETE',
        credentials: 'include'
      });

      const result = await response.json();

      if (response.ok && result.success) {
        toast.success('Route stop deleted successfully');
        fetchRouteStops(selectedBus);
      } else {
        toast.error(result.error || 'Failed to delete route stop');
      }
    } catch (error) {
      toast.error('Failed to delete route stop');
    }
  };

  const getNextStopOrder = () => {
    if (routeStops.length === 0) return 1;
    return Math.max(...routeStops.map(stop => stop.stop_order)) + 1;
  };

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-red-600"></div>
      </div>
    );
  }

  return (
    <PageTransition>
      <div className="min-h-screen p-4 bg-gray-50">
        <div className="max-w-7xl mx-auto">
          {/* Header */}
          <motion.div
            initial={{ opacity: 0, y: -20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            className="flex justify-between items-center mb-8"
          >
            <div className="flex items-center gap-4">
              <Link href="/admin/dashboard">
                <Button variant="outline" size="sm">
                  <ArrowLeft className="w-4 h-4 mr-2" />
                  Back to Dashboard
                </Button>
              </Link>
              <div>
                <h1 className="text-3xl font-bold text-gray-800 mb-2">Route Management</h1>
                <p className="text-gray-600">Manage bus routes and stops efficiently</p>
              </div>
            </div>
          </motion.div>

          {/* Bus Selection */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.1 }}
            className="mb-8"
          >
            <Card className="shadow-sm">
              <CardHeader>
                <CardTitle className="flex items-center gap-2 text-xl">
                  <Route className="w-6 h-6 text-red-600" />
                  Select Bus Route
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex gap-4 items-end">
                  <div className="flex-1">
                    <Label htmlFor="bus-select" className="text-sm font-medium text-gray-700 mb-2 block">
                      Bus Route
                    </Label>
                    <Select value={selectedBus} onValueChange={setSelectedBus}>
                      <SelectTrigger className="h-11">
                        <SelectValue placeholder="Choose a bus route to manage" />
                      </SelectTrigger>
                      <SelectContent>
                        {buses.map((bus) => (
                          <SelectItem key={bus.route_code} value={bus.route_code}>
                            <div className="flex items-center gap-2">
                              <span className="font-medium">{bus.name}</span>
                              <Badge variant="outline" className="text-xs">{bus.route_code}</Badge>
                            </div>
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                  {selectedBus && (
                    <Button 
                      onClick={openCreateModal}
                      className="bg-gradient-to-r from-red-600 to-orange-600 hover:from-red-700 hover:to-orange-700 h-11 px-6"
                    >
                      <Plus className="w-4 h-4 mr-2" />
                      Add New Stop
                    </Button>
                  )}
                </div>
              </CardContent>
            </Card>
          </motion.div>

          {/* Route Stops List */}
          {selectedBus && (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.2 }}
            >
              <Card className="shadow-sm">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2 text-xl">
                    <MapPin className="w-6 h-6 text-red-600" />
                    Route Stops - {buses.find(b => b.route_code === selectedBus)?.name}
                    <Badge variant="secondary" className="ml-2">
                      {routeStops.length} stops
                    </Badge>
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  {routeStops.length > 0 ? (
                    <div className="grid gap-4">
                      {routeStops.map((stop, index) => (
                        <motion.div
                          key={stop.id}
                          initial={{ opacity: 0, x: -20 }}
                          animate={{ opacity: 1, x: 0 }}
                          transition={{ duration: 0.3, delay: index * 0.05 }}
                          className="flex items-center justify-between p-6 border border-gray-200 rounded-xl hover:bg-gray-50 hover:border-gray-300 transition-all duration-200"
                        >
                          <div className="flex items-center gap-6">
                            <div className="flex items-center justify-center w-12 h-12 rounded-full bg-gradient-to-br from-red-100 to-orange-100 text-red-600 font-bold text-lg border-2 border-red-200">
                              {stop.stop_order}
                            </div>
                            <div>
                              <h4 className="font-semibold text-gray-800 text-lg mb-1">{stop.stop_name}</h4>
                              <div className="flex items-center gap-4 text-sm text-gray-600">
                                <span className="flex items-center gap-2 bg-green-100 text-green-700 px-3 py-1 rounded-full">
                                  <IndianRupee className="w-4 h-4" />
                                  ₹{stop.fare}
                                </span>
                                <Badge 
                                  variant={stop.is_active ? "default" : "secondary"} 
                                  className={`text-xs ${stop.is_active ? 'bg-green-100 text-green-700' : 'bg-gray-100 text-gray-600'}`}
                                >
                                  {stop.is_active ? "Active" : "Inactive"}
                                </Badge>
                              </div>
                            </div>
                          </div>
                          <div className="flex gap-3">
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={() => openEditModal(stop)}
                              className="border-blue-200 text-blue-700 hover:bg-blue-50"
                            >
                              <Edit className="w-4 h-4 mr-2" />
                              Edit
                            </Button>
                            <Button
                              size="sm"
                              variant="outline"
                              className="border-red-200 text-red-600 hover:bg-red-50"
                              onClick={() => handleDelete(stop.id!)}
                            >
                              <Trash2 className="w-4 h-4 mr-2" />
                              Delete
                            </Button>
                          </div>
                        </motion.div>
                      ))}
                    </div>
                  ) : (
                    <div className="text-center py-16">
                      <div className="w-20 h-20 mx-auto bg-gray-100 rounded-full flex items-center justify-center mb-4">
                        <MapPin className="w-10 h-10 text-gray-400" />
                      </div>
                      <h3 className="text-xl font-semibold text-gray-600 mb-2">No stops found</h3>
                      <p className="text-gray-500 mb-6">Start building your route by adding the first stop</p>
                      <Button 
                        onClick={openCreateModal}
                        className="bg-gradient-to-r from-red-600 to-orange-600 hover:from-red-700 hover:to-orange-700 px-8 py-3"
                      >
                        <Plus className="w-5 h-5 mr-2" />
                        Add First Stop
                      </Button>
                    </div>
                  )}
                </CardContent>
              </Card>
            </motion.div>
          )}

          {/* No Bus Selected */}
          {!selectedBus && (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.2 }}
              className="text-center py-20"
            >
              <div className="w-24 h-24 mx-auto bg-gray-100 rounded-full flex items-center justify-center mb-6">
                <Route className="w-12 h-12 text-gray-400" />
              </div>
              <h3 className="text-xl font-semibold text-gray-600 mb-3">Select a Bus Route</h3>
              <p className="text-gray-500 text-lg">Choose a bus route above to start managing its stops and fares</p>
            </motion.div>
          )}
        </div>

        {/* Modal */}
        <Dialog open={isModalOpen} onOpenChange={setIsModalOpen}>
          <DialogContent className="sm:max-w-md">
            <DialogHeader>
              <DialogTitle className="flex items-center gap-2">
                {isEditMode ? (
                  <>
                    <Edit className="w-5 h-5 text-blue-600" />
                    Edit Route Stop
                  </>
                ) : (
                  <>
                    <Plus className="w-5 h-5 text-green-600" />
                    Add New Route Stop
                  </>
                )}
              </DialogTitle>
            </DialogHeader>
            
            <div className="space-y-4 py-4">
              <div>
                <Label htmlFor="stop-name" className="text-sm font-medium">
                  Stop Name *
                </Label>
                <Input
                  id="stop-name"
                  value={formData.stop_name}
                  onChange={(e) => setFormData({ ...formData, stop_name: e.target.value })}
                  placeholder="e.g., Kottayam"
                  className={`mt-1 ${errors.stop_name ? 'border-red-500' : ''}`}
                />
                {errors.stop_name && (
                  <p className="text-red-500 text-xs mt-1 flex items-center gap-1">
                    <AlertCircle className="w-3 h-3" />
                    {errors.stop_name}
                  </p>
                )}
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="fare" className="text-sm font-medium">
                    Fare (₹) *
                  </Label>
                  <Input
                    id="fare"
                    type="text"
                    inputMode="numeric"
                    pattern="[0-9]*"
                    value={formData.fare}
                    onChange={(e) => {
                      const value = e.target.value.replace(/[^0-9]/g, '');
                      const numValue = value === '' ? 1 : parseInt(value, 10);
                      setFormData({ ...formData, fare: numValue });
                    }}
                    onBlur={(e) => {
                      if (formData.fare <= 0) {
                        setFormData({ ...formData, fare: 1 });
                      }
                    }}
                    placeholder="50"
                    className={`mt-1 ${errors.fare ? 'border-red-500' : ''}`}
                  />
                  {errors.fare && (
                    <p className="text-red-500 text-xs mt-1 flex items-center gap-1">
                      <AlertCircle className="w-3 h-3" />
                      {errors.fare}
                    </p>
                  )}
                </div>

                <div>
                  <Label htmlFor="stop-order" className="text-sm font-medium">
                    Stop Order *
                  </Label>
                  <Input
                    id="stop-order"
                    type="number"
                    min="1"
                    value={formData.stop_order}
                    onChange={(e) => setFormData({ ...formData, stop_order: parseInt(e.target.value) || 1 })}
                    className={`mt-1 ${errors.stop_order ? 'border-red-500' : ''}`}
                  />
                  {errors.stop_order && (
                    <p className="text-red-500 text-xs mt-1 flex items-center gap-1">
                      <AlertCircle className="w-3 h-3" />
                      {errors.stop_order}
                    </p>
                  )}
                </div>
              </div>

              <div className="flex items-center space-x-2">
                <Switch
                  id="is-active"
                  checked={formData.is_active}
                  onCheckedChange={(checked) => setFormData({ ...formData, is_active: checked })}
                />
                <Label htmlFor="is-active" className="text-sm font-medium">
                  Active
                </Label>
              </div>
            </div>

            <div className="flex gap-3 pt-4 border-t">
              <Button 
                onClick={handleSubmit}
                className="flex-1 bg-gradient-to-r from-red-600 to-orange-600 hover:from-red-700 hover:to-orange-700"
              >
                <Save className="w-4 h-4 mr-2" />
                {isEditMode ? 'Update Stop' : 'Add Stop'}
              </Button>
              <Button variant="outline" onClick={closeModal} className="flex-1">
                <X className="w-4 h-4 mr-2" />
                Cancel
              </Button>
            </div>
          </DialogContent>
        </Dialog>
      </div>
    </PageTransition>
  );
}

export default withAdminAuth(RouteManagement);
'use client';

import React, { useState, useEffect, useCallback } from 'react';
import { Card, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { ChevronLeft, ChevronRight, Search, Download, Filter, Crown } from 'lucide-react';
import { toast } from 'sonner';

interface DateRange {
  startDate: string;
  endDate: string;
}

interface Booking {
  id: number;
  admission_number: string;
  student_name: string;
  routeName: string;
  destination: string;
  payment_status: boolean;
  fare: number;
  is_special: boolean | null;
  created_at: string;
  updated_at: string;
}

interface BookingsData {
  bookings: Booking[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
  summary: {
    totalBookings: number;
    paidBookings: number;
    unpaidBookings: number;
    totalRevenue: number;
    uniqueRoutes: number;
  };
  availableRoutes: string[];
}

interface BookingsTabProps {
  dateRange: DateRange;
  isRefreshing: boolean;
}

export default function BookingsTab({ dateRange, isRefreshing }: BookingsTabProps) {
  const [data, setData] = useState<BookingsData | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  
  // Filters and pagination
  const [currentPage, setCurrentPage] = useState(1);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedRoute, setSelectedRoute] = useState<string>('all');
  const [paymentFilter, setPaymentFilter] = useState<string>('all');
  const [pageSize] = useState(25);

  const fetchBookingsData = useCallback(async () => {
    setIsLoading(true);
    setError(null);

    try {
      const params = new URLSearchParams({
        start_date: dateRange.startDate,
        end_date: dateRange.endDate,
        page: currentPage.toString(),
        limit: pageSize.toString()
      });

      if (selectedRoute !== 'all') {
        params.append('bus_route', selectedRoute);
      }

      if (paymentFilter !== 'all') {
        params.append('payment_status', paymentFilter);
      }

      const response = await fetch(`/api/admin/custom-reports/bookings?${params}`, {
        credentials: 'include'
      });

      const result = await response.json();

      if (result.success) {
        setData(result.data);
      } else {
        setError(result.error || 'Failed to fetch bookings data');
        toast.error('Failed to fetch bookings data');
      }
    } catch (error) {
      setError('Network error while fetching bookings data');
      toast.error('Network error while fetching bookings data');
      console.error('Bookings fetch error:', error);
    } finally {
      setIsLoading(false);
    }
  }, [dateRange, currentPage, pageSize, selectedRoute, paymentFilter]);

  // Fetch data when dependencies change
  useEffect(() => {
    fetchBookingsData();
  }, [fetchBookingsData]);

  // Reset page when filters change
  useEffect(() => {
    setCurrentPage(1);
  }, [selectedRoute, paymentFilter, searchTerm]);

  const handlePageChange = (newPage: number) => {
    setCurrentPage(newPage);
  };

  const handleExport = () => {
    if (!data) return;
    
    // Create CSV content
    const headers = ['ID', 'Special Booking', 'Admission Number', 'Student Name', 'Route Name', 'Destination', 'Payment Status', 'Fare', 'Created At'];
    const csvContent = [
      headers.join(','),
      ...data.bookings.map(booking => [
        booking.id,
        booking.is_special ? 'Yes' : 'No',
        booking.admission_number,
        `"${booking.student_name}"`,
        booking.routeName,
        `"${booking.destination}"`,
        booking.payment_status ? 'Paid' : 'Unpaid',
        booking.fare || 0,
        new Date(booking.created_at).toLocaleString()
      ].join(','))
    ].join('\n');

    // Download CSV
    const blob = new Blob([csvContent], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `bookings-${dateRange.startDate}-to-${dateRange.endDate}.csv`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    window.URL.revokeObjectURL(url);
    
    toast.success('Bookings data exported successfully');
  };

  // Filter bookings by search term (client-side)
  const filteredBookings = data?.bookings.filter(booking => {
    if (!searchTerm) return true;
    const term = searchTerm.toLowerCase();
    return (
      booking.admission_number.toLowerCase().includes(term) ||
      booking.student_name.toLowerCase().includes(term) ||
      booking.routeName.toLowerCase().includes(term) ||
      booking.destination.toLowerCase().includes(term)
    );
  }) || [];

  if (isLoading) {
    return (
      <div className="flex items-center justify-center py-12">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading bookings data...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="text-center py-12">
        <p className="text-red-600 mb-4">{error}</p>
        <Button onClick={fetchBookingsData} variant="outline">
          Try Again
        </Button>
      </div>
    );
  }

  if (!data) {
    return (
      <div className="text-center py-12">
        <p className="text-gray-600">No data available</p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Summary Cards */}
      <div className="grid grid-cols-2 md:grid-cols-5 gap-4">
        <Card>
          <CardContent className="p-4 text-center">
            <div className="text-2xl font-bold text-blue-600">{data.summary.totalBookings}</div>
            <div className="text-sm text-gray-600">Total Bookings</div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4 text-center">
            <div className="text-2xl font-bold text-green-600">{data.summary.paidBookings}</div>
            <div className="text-sm text-gray-600">Paid Bookings</div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4 text-center">
            <div className="text-2xl font-bold text-orange-600">{data.summary.unpaidBookings}</div>
            <div className="text-sm text-gray-600">Unpaid Bookings</div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4 text-center">
            <div className="text-2xl font-bold text-purple-600">₹{data.summary.totalRevenue}</div>
            <div className="text-sm text-gray-600">Total Revenue</div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4 text-center">
            <div className="text-2xl font-bold text-indigo-600">{data.summary.uniqueRoutes}</div>
            <div className="text-sm text-gray-600">Unique Routes</div>
          </CardContent>
        </Card>
      </div>

      {/* Filters and Search */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <span className="flex items-center gap-2">
              <Filter className="w-5 h-5" />
              Filters & Search
            </span>
            <Button onClick={handleExport} variant="outline" size="sm">
              <Download className="w-4 h-4 mr-2" />
              Export CSV
            </Button>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div>
              <Input
                placeholder="Search by name, admission number, route name..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full"
              />
            </div>
            <div>
              <Select value={selectedRoute} onValueChange={setSelectedRoute}>
                <SelectTrigger>
                  <SelectValue placeholder="Filter by route name" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Routes</SelectItem>
                  {data.availableRoutes.map(route => (
                    <SelectItem key={route} value={route}>{route}</SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <div>
              <Select value={paymentFilter} onValueChange={setPaymentFilter}>
                <SelectTrigger>
                  <SelectValue placeholder="Filter by payment" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Payments</SelectItem>
                  <SelectItem value="true">Paid</SelectItem>
                  <SelectItem value="false">Unpaid</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div>
              <Button 
                onClick={() => {
                  setSearchTerm('');
                  setSelectedRoute('all');
                  setPaymentFilter('all');
                }} 
                variant="outline" 
                className="w-full"
              >
                Clear Filters
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Bookings Table */}
      <Card>
        <CardHeader>
          <CardTitle>Bookings List</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="overflow-x-auto">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>ID</TableHead>
                  <TableHead>Admission Number</TableHead>
                  <TableHead>Student Name</TableHead>
                  <TableHead>Route Name</TableHead>
                  <TableHead>Destination</TableHead>
                  <TableHead>Payment Status</TableHead>
                  <TableHead>Fare</TableHead>
                  <TableHead>Created At</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredBookings.length > 0 ? (
                  filteredBookings.map((booking) => (
                    <TableRow key={booking.id}>
                      <TableCell className="flex items-center gap-2">
                        {booking.id}
                        {booking.is_special && (
                          <Crown className="w-4 h-4 text-yellow-500">
                            <title>Admin Booked</title>
                          </Crown>
                        )}
                      </TableCell>
                      <TableCell className="font-medium">{booking.admission_number}</TableCell>
                      <TableCell>{booking.student_name}</TableCell>
                      <TableCell>{booking.routeName}</TableCell>
                      <TableCell>{booking.destination}</TableCell>
                      <TableCell>
                        <Badge variant={booking.payment_status ? "default" : "secondary"}>
                          {booking.payment_status ? 'Paid' : 'Unpaid'}
                        </Badge>
                      </TableCell>
                      <TableCell>₹{booking.fare || 0}</TableCell>
                      <TableCell>{new Date(booking.created_at).toLocaleDateString()}</TableCell>
                    </TableRow>
                  ))
                ) : (
                  <TableRow>
                    <TableCell colSpan={8} className="text-center py-8 text-gray-500">
                      No bookings found for the selected criteria
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
          </div>

          {/* Pagination */}
          {data.pagination.totalPages > 1 && (
            <div className="flex items-center justify-between mt-6">
              <div className="text-sm text-gray-600">
                Showing {((currentPage - 1) * pageSize) + 1} to {Math.min(currentPage * pageSize, data.pagination.total)} of {data.pagination.total} results
              </div>
              <div className="flex items-center gap-2">
                <Button
                  onClick={() => handlePageChange(currentPage - 1)}
                  disabled={currentPage === 1}
                  variant="outline"
                  size="sm"
                >
                  <ChevronLeft className="w-4 h-4" />
                  Previous
                </Button>
                <span className="text-sm">
                  Page {currentPage} of {data.pagination.totalPages}
                </span>
                <Button
                  onClick={() => handlePageChange(currentPage + 1)}
                  disabled={currentPage === data.pagination.totalPages}
                  variant="outline"
                  size="sm"
                >
                  Next
                  <ChevronRight className="w-4 h-4" />
                </Button>
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}

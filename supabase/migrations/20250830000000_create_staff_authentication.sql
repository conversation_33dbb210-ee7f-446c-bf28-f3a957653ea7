/*
  # Staff Authentication System
  
  This migration creates the staff authentication system with:
  - staff_users: Staff user accounts with route-based access
  - staff_sessions: Session management for staff authentication
  
  Follows the same security patterns as the existing admin authentication system.
*/

-- Create staff_users table
CREATE TABLE IF NOT EXISTS staff_users (
  id SERIAL PRIMARY KEY,
  username VARCHAR(50) UNIQUE NOT NULL,
  password_hash VARCHAR(255) NOT NULL,
  route_code VARCHAR(50) NOT NULL,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  
  -- Foreign key constraint to ensure route_code exists in buses table
  CONSTRAINT fk_staff_route_code FOREIGN KEY (route_code) REFERENCES buses(route_code) ON DELETE RESTRICT
);

-- Create staff_sessions table
CREATE TABLE IF NOT EXISTS staff_sessions (
  id SERIAL PRIMARY KEY,
  staff_id INTEGER NOT NULL REFERENCES staff_users(id) ON DELETE CASCADE,
  session_token VARCHAR(255) UNIQUE NOT NULL,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  expires_at TIMESTAMPTZ NOT NULL
);

-- Enable Row Level Security
ALTER TABLE staff_users ENABLE ROW LEVEL SECURITY;
ALTER TABLE staff_sessions ENABLE ROW LEVEL SECURITY;

-- Create policies for staff_users (only authenticated users can manage)
CREATE POLICY "Allow authenticated staff_users management"
  ON staff_users
  FOR ALL
  TO authenticated
  USING (true)
  WITH CHECK (true);

-- Create policies for staff_sessions (only authenticated users can manage their sessions)
CREATE POLICY "Users can manage their own staff sessions"
  ON staff_sessions
  FOR ALL
  TO authenticated
  USING (true);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_staff_users_username ON staff_users(username);
CREATE INDEX IF NOT EXISTS idx_staff_users_route_code ON staff_users(route_code);
CREATE INDEX IF NOT EXISTS idx_staff_sessions_staff_id ON staff_sessions(staff_id);
CREATE INDEX IF NOT EXISTS idx_staff_sessions_session_token ON staff_sessions(session_token);
CREATE INDEX IF NOT EXISTS idx_staff_sessions_expires_at ON staff_sessions(expires_at);

-- Create trigger for updated_at on staff_users
CREATE TRIGGER update_staff_users_updated_at
  BEFORE UPDATE ON staff_users
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at_column();

-- Create function to clean expired staff sessions
CREATE OR REPLACE FUNCTION clean_expired_staff_sessions()
RETURNS VOID AS $$
BEGIN
  DELETE FROM staff_sessions WHERE expires_at < NOW();
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Insert sample staff users for testing (password: staff123)
-- First, let's check if we have existing buses and use their route codes
-- If no buses exist, create sample ones for testing
DO $$
DECLARE
    route_codes TEXT[];
BEGIN
    -- Get existing route codes from buses table
    SELECT ARRAY_AGG(route_code) INTO route_codes FROM buses LIMIT 3;
    
    -- If no buses exist, create sample ones
    IF route_codes IS NULL OR array_length(route_codes, 1) IS NULL THEN
        INSERT INTO buses (name, route_code, total_seats, is_active) VALUES
            ('Sample Bus 1', 'route-1', 40, true),
            ('Sample Bus 2', 'route-2', 40, true),
            ('Sample Bus 3', 'route-3', 40, true)
        ON CONFLICT (route_code) DO NOTHING;
        
        -- Create corresponding bus availability records
        INSERT INTO bus_availability (bus_route, available_seats) VALUES
            ('route-1', 40),
            ('route-2', 40),
            ('route-3', 40)
        ON CONFLICT (bus_route) DO NOTHING;
        
        route_codes := ARRAY['route-1', 'route-2', 'route-3'];
    END IF;
    
    -- Insert sample staff users using existing or newly created route codes
    -- Password hash for 'staff123' using bcrypt with 10 rounds
    INSERT INTO staff_users (username, password_hash, route_code) VALUES
        ('staff_route1', '$2b$10$K8BEyIXM2wMvWO7YdlzOOeEHrZxmBdZVXseVMvvJ6.H0kUjYVUjYm', route_codes[1]),
        ('staff_route2', '$2b$10$K8BEyIXM2wMvWO7YdlzOOeEHrZxmBdZVXseVMvvJ6.H0kUjYVUjYm', COALESCE(route_codes[2], route_codes[1])),
        ('staff_route3', '$2b$10$K8BEyIXM2wMvWO7YdlzOOeEHrZxmBdZVXseVMvvJ6.H0kUjYVUjYm', COALESCE(route_codes[3], route_codes[1]))
    ON CONFLICT (username) DO NOTHING;
END $$;

-- Grant necessary permissions
GRANT SELECT, INSERT, UPDATE, DELETE ON staff_users TO authenticated;
GRANT SELECT, INSERT, UPDATE, DELETE ON staff_sessions TO authenticated;
GRANT USAGE, SELECT ON SEQUENCE staff_users_id_seq TO authenticated;
GRANT USAGE, SELECT ON SEQUENCE staff_sessions_id_seq TO authenticated;

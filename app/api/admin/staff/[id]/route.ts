export const dynamic = "force-dynamic";

import { NextRequest, NextResponse } from 'next/server';
import { withAdminAuth, getUser } from '@/lib/middleware';
import { supabaseAdmin } from '@/lib/supabase';
import { createApiResponse, handleApiError, validateRequestBody } from '@/lib/middleware';

// PUT - Update staff user
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  return withAdminAuth(request, async (req) => {
    try {
      const user = getUser(req);
      if (!user) {
        return NextResponse.json(
          { error: 'Admin authentication required' },
          { status: 401 }
        );
      }

      const staffId = parseInt(params.id);
      if (isNaN(staffId)) {
        return NextResponse.json(
          { error: 'Invalid staff ID' },
          { status: 400 }
        );
      }

      const body = await request.json();

      // Validate request body
      const validation = validateRequestBody<{
        username: string;
        route_code: string;
      }>(body, ['username', 'route_code']);

      if (!validation.isValid) {
        return NextResponse.json(
          { error: 'Validation failed', details: validation.errors },
          { status: 400 }
        );
      }

      const { username, route_code } = body;

      // Check if staff user exists
      const { data: existingStaff } = await supabaseAdmin
        .from('staff_users')
        .select('id, username')
        .eq('id', staffId)
        .single();

      if (!existingStaff) {
        return NextResponse.json(
          { error: 'Staff user not found' },
          { status: 404 }
        );
      }

      // Check if username already exists (excluding current user)
      if (username !== existingStaff.username) {
        const { data: duplicateUser } = await supabaseAdmin
          .from('staff_users')
          .select('id')
          .eq('username', username)
          .neq('id', staffId)
          .single();

        if (duplicateUser) {
          return NextResponse.json(
            { error: 'Username already exists' },
            { status: 400 }
          );
        }
      }

      // Verify route_code exists in buses table
      const { data: bus } = await supabaseAdmin
        .from('buses')
        .select('route_code')
        .eq('route_code', route_code)
        .single();

      if (!bus) {
        return NextResponse.json(
          { error: 'Invalid route code' },
          { status: 400 }
        );
      }

      // Update staff user
      const { data: updatedStaff, error: updateError } = await supabaseAdmin
        .from('staff_users')
        .update({
          username,
          route_code,
          updated_at: new Date().toISOString()
        })
        .eq('id', staffId)
        .select('id, username, route_code, created_at, updated_at')
        .single();

      if (updateError) {
        console.error('Error updating staff user:', updateError);
        return NextResponse.json(
          { error: 'Failed to update staff user' },
          { status: 500 }
        );
      }

      return NextResponse.json(
        createApiResponse(updatedStaff)
      );
    } catch (error) {
      return handleApiError(error, 'Failed to update staff user');
    }
  });
}

// DELETE - Delete staff user
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  return withAdminAuth(request, async (req) => {
    try {
      const user = getUser(req);
      if (!user) {
        return NextResponse.json(
          { error: 'Admin authentication required' },
          { status: 401 }
        );
      }

      const staffId = parseInt(params.id);
      if (isNaN(staffId)) {
        return NextResponse.json(
          { error: 'Invalid staff ID' },
          { status: 400 }
        );
      }

      // Check if staff user exists
      const { data: existingStaff } = await supabaseAdmin
        .from('staff_users')
        .select('id, username')
        .eq('id', staffId)
        .single();

      if (!existingStaff) {
        return NextResponse.json(
          { error: 'Staff user not found' },
          { status: 404 }
        );
      }

      // Delete staff user (this will also cascade delete sessions due to foreign key constraint)
      const { error: deleteError } = await supabaseAdmin
        .from('staff_users')
        .delete()
        .eq('id', staffId);

      if (deleteError) {
        console.error('Error deleting staff user:', deleteError);
        return NextResponse.json(
          { error: 'Failed to delete staff user' },
          { status: 500 }
        );
      }

      return NextResponse.json(
        createApiResponse({ 
          message: 'Staff user deleted successfully',
          deletedUser: existingStaff
        })
      );
    } catch (error) {
      return handleApiError(error, 'Failed to delete staff user');
    }
  });
}

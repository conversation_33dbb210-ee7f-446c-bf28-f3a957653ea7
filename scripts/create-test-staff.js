// Script to create test staff users for debugging
console.log('🔧 Creating test staff users for debugging...\n');

const { createClient } = require('@supabase/supabase-js');
const bcrypt = require('bcryptjs');

// Initialize Supabase client
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || 'http://localhost:54321';
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY || 'your-service-role-key';

console.log('Supabase URL:', supabaseUrl);
console.log('Service Key:', supabaseServiceKey ? 'Set' : 'Not Set');

if (!supabaseServiceKey || supabaseServiceKey === 'your-service-role-key') {
  console.log('❌ Please set SUPABASE_SERVICE_ROLE_KEY environment variable');
  console.log('Or update this script with your actual service role key');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function createTestStaffUsers() {
  try {
    console.log('1. Checking if staff_users table exists...');
    
    // Check if staff_users table exists
    const { data: tables, error: tablesError } = await supabase
      .from('information_schema.tables')
      .select('table_name')
      .eq('table_schema', 'public')
      .eq('table_name', 'staff_users');

    if (tablesError) {
      console.log('❌ Error checking tables:', tablesError);
      return;
    }

    if (tables.length === 0) {
      console.log('❌ staff_users table does not exist. Please run migrations first.');
      return;
    }

    console.log('✅ staff_users table exists');

    // Check if buses table exists and has route codes
    console.log('\n2. Checking buses table...');
    const { data: buses, error: busesError } = await supabase
      .from('buses')
      .select('route_code, name');

    if (busesError) {
      console.log('❌ Error fetching buses:', busesError);
      return;
    }

    if (buses.length === 0) {
      console.log('❌ No buses found. Creating sample buses first...');
      
      // Create sample buses
      const { data: newBuses, error: createBusesError } = await supabase
        .from('buses')
        .insert([
          { name: 'Test Bus 1', route_code: 'route-1', total_seats: 40, is_active: true },
          { name: 'Test Bus 2', route_code: 'route-2', total_seats: 40, is_active: true },
          { name: 'Test Bus 3', route_code: 'route-3', total_seats: 40, is_active: true }
        ])
        .select();

      if (createBusesError) {
        console.log('❌ Error creating buses:', createBusesError);
        return;
      }

      console.log('✅ Created sample buses:', newBuses);
    } else {
      console.log(`✅ Found ${buses.length} existing buses:`, buses.map(b => b.route_code));
    }

    // Get route codes for staff users
    const { data: availableRoutes } = await supabase
      .from('buses')
      .select('route_code')
      .limit(3);

    if (!availableRoutes || availableRoutes.length === 0) {
      console.log('❌ No route codes available for staff users');
      return;
    }

    console.log('\n3. Creating test staff users...');
    
    // Create test staff users
    const testStaffUsers = [
      {
        username: 'staff_route1',
        password: 'staff123',
        route_code: availableRoutes[0].route_code
      },
      {
        username: 'staff_route2',
        password: 'staff123',
        route_code: availableRoutes[1] ? availableRoutes[1].route_code : availableRoutes[0].route_code
      },
      {
        username: 'staff_route3',
        password: 'staff123',
        route_code: availableRoutes[2] ? availableRoutes[2].route_code : availableRoutes[0].route_code
      }
    ];

    for (const staffUser of testStaffUsers) {
      try {
        // Hash password
        const passwordHash = await bcrypt.hash(staffUser.password, 10);
        
        // Check if user already exists
        const { data: existingUser } = await supabase
          .from('staff_users')
          .select('id')
          .eq('username', staffUser.username)
          .single();

        if (existingUser) {
          console.log(`⚠️  Staff user ${staffUser.username} already exists, skipping...`);
          continue;
        }

        // Create staff user
        const { data: newStaff, error: createError } = await supabase
          .from('staff_users')
          .insert({
            username: staffUser.username,
            password_hash: passwordHash,
            route_code: staffUser.route_code
          })
          .select()
          .single();

        if (createError) {
          console.log(`❌ Error creating staff user ${staffUser.username}:`, createError);
        } else {
          console.log(`✅ Created staff user: ${staffUser.username} (Route: ${staffUser.route_code})`);
        }
      } catch (error) {
        console.log(`❌ Error processing staff user ${staffUser.username}:`, error);
      }
    }

    // Verify created staff users
    console.log('\n4. Verifying created staff users...');
    const { data: allStaff, error: fetchError } = await supabase
      .from('staff_users')
      .select('username, route_code, created_at');

    if (fetchError) {
      console.log('❌ Error fetching staff users:', fetchError);
      return;
    }

    console.log(`✅ Total staff users in database: ${allStaff.length}`);
    allStaff.forEach((staff, index) => {
      console.log(`  ${index + 1}. ${staff.username} - Route: ${staff.route_code}`);
    });

  } catch (error) {
    console.log('❌ Error in createTestStaffUsers:', error);
  }
}

async function runStaffCreation() {
  console.log('🚀 Starting staff user creation...\n');
  
  await createTestStaffUsers();
  
  console.log('\n🎯 Staff Creation Summary:');
  console.log('Check the output above for any ❌ errors or ✅ successes.');
  console.log('If successful, you can now test staff login with:');
  console.log('  Username: staff_route1, Password: staff123');
}

// Run the staff creation
runStaffCreation().catch(console.error); 
export const dynamic = "force-dynamic";

import { NextRequest, NextResponse } from 'next/server';
import { withStaffAuth, getStaff } from '@/lib/middleware';
import { supabaseAdmin } from '@/lib/supabase';
import { createApiResponse, handleApiError } from '@/lib/middleware';

export async function GET(request: NextRequest) {
  return withStaffAuth(request, async (req) => {
    try {
      const staff = getStaff(req);
      if (!staff) {
        return NextResponse.json(
          { error: 'Staff authentication required' },
          { status: 401 }
        );
      }

      const { searchParams } = new URL(request.url);
      const routeCode = searchParams.get('route') || staff.route_code;

      // Verify staff can only access their own route data
      if (routeCode !== staff.route_code) {
        return NextResponse.json(
          { error: 'Access denied: You can only view bookings for your assigned route' },
          { status: 403 }
        );
      }

      // Fetch historical bookings for the staff's route from the bookings table
      const { data: bookings, error: bookingsError } = await supabaseAdmin
        .from('bookings')
        .select(`
          id,
          admission_number,
          student_name,
          bus_route,
          destination,
          created_at,
          go_date,
          return_date,
          is_special
        `)
        .eq('bus_route', routeCode)
        .order('created_at', { ascending: false });

      if (bookingsError) {
        console.error('Error fetching historical bookings:', bookingsError);
        return NextResponse.json(
          { error: 'Failed to fetch historical bookings' },
          { status: 500 }
        );
      }

      return NextResponse.json({
        success: true,
        data: {
          bookings: bookings || [],
          route_code: routeCode,
          staff_info: {
            username: staff.username,
            route_code: staff.route_code
          }
        }
      });
    } catch (error) {
      return handleApiError(error, 'Failed to fetch historical bookings');
    }
  });
} 
// Debug script for staff API authentication and booking data fetching
console.log('🔍 Debugging Staff API Authentication System...\n');

const BASE_URL = 'http://localhost:3000';

// Test 1: Check if staff login endpoint exists and works
async function testStaffLogin() {
  console.log('Test 1: Staff Login Endpoint');
  try {
    const response = await fetch(`${BASE_URL}/api/staff/login`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        username: 'staff_route1',
        password: 'staff123'
      })
    });

    console.log('Login Response Status:', response.status);
    const result = await response.json();
    console.log('Login Response:', JSON.stringify(result, null, 2));

    if (response.ok && result.success) {
      console.log('✅ Staff login successful');
      return result.data.token;
    } else {
      console.log('❌ Staff login failed:', result.error);
      return null;
    }
  } catch (error) {
    console.log('❌ Staff login error:', error.message);
    return null;
  }
}

// Test 2: Test staff session validation
async function testStaffValidation(token) {
  console.log('\nTest 2: Staff Session Validation');
  if (!token) {
    console.log('❌ No token to validate');
    return false;
  }

  try {
    const response = await fetch(`${BASE_URL}/api/staff/validate`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json',
      }
    });

    console.log('Validation Response Status:', response.status);
    const result = await response.json();
    console.log('Validation Response:', JSON.stringify(result, null, 2));

    if (response.ok && result.success && result.data.valid) {
      console.log('✅ Staff session validation successful');
      return result.data.staff;
    } else {
      console.log('❌ Staff session validation failed:', result.error);
      return false;
    }
  } catch (error) {
    console.log('❌ Staff validation error:', error.message);
    return false;
  }
}

// Test 3: Test staff bookings API with authentication
async function testStaffBookingsAPI(token, routeCode) {
  console.log('\nTest 3: Staff Bookings API');
  if (!token) {
    console.log('❌ No token to test bookings API');
    return false;
  }

  try {
    const response = await fetch(`${BASE_URL}/api/staff/bookings?route=${routeCode}`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json',
      }
    });

    console.log('Bookings API Response Status:', response.status);
    const result = await response.json();
    console.log('Bookings API Response:', JSON.stringify(result, null, 2));

    if (response.ok && result.success) {
      console.log('✅ Staff bookings API successful');
      console.log('Bookings count:', result.data.bookings?.length || 0);
      console.log('Stats:', result.data.stats);
      console.log('Route code:', result.data.route_code);
      console.log('Bus name:', result.data.bus_name);
      return true;
    } else {
      console.log('❌ Staff bookings API failed:', result.error);
      return false;
    }
  } catch (error) {
    console.log('❌ Staff bookings API error:', error.message);
    return false;
  }
}

// Test 4: Test staff historical bookings API
async function testStaffHistoricalBookingsAPI(token, routeCode) {
  console.log('\nTest 4: Staff Historical Bookings API');
  if (!token) {
    console.log('❌ No token to test historical bookings API');
    return false;
  }

  try {
    const response = await fetch(`${BASE_URL}/api/staff/historical-bookings?route=${routeCode}`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json',
      }
    });

    console.log('Historical Bookings API Response Status:', response.status);
    const result = await response.json();
    console.log('Historical Bookings API Response:', JSON.stringify(result, null, 2));

    if (response.ok && result.success) {
      console.log('✅ Staff historical bookings API successful');
      console.log('Historical bookings count:', result.data.bookings?.length || 0);
      return true;
    } else {
      console.log('❌ Staff historical bookings API failed:', result.error);
      return false;
    }
  } catch (error) {
    console.log('❌ Staff historical bookings API error:', error.message);
    return false;
  }
}

// Test 5: Test without authentication (should fail)
async function testUnauthenticatedAccess() {
  console.log('\nTest 5: Unauthenticated Access (Should Fail)');
  
  try {
    const response = await fetch(`${BASE_URL}/api/staff/bookings?route=route-1`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      }
    });

    console.log('Unauthenticated Response Status:', response.status);
    const result = await response.json();
    console.log('Unauthenticated Response:', JSON.stringify(result, null, 2));

    if (response.status === 401) {
      console.log('✅ Unauthenticated access properly blocked');
      return true;
    } else {
      console.log('❌ Unauthenticated access not properly blocked');
      return false;
    }
  } catch (error) {
    console.log('❌ Unauthenticated access test error:', error.message);
    return false;
  }
}

// Test 6: Test with invalid route (should fail for staff)
async function testInvalidRouteAccess(token, invalidRoute) {
  console.log('\nTest 6: Invalid Route Access (Should Fail)');
  if (!token) {
    console.log('❌ No token to test invalid route access');
    return false;
  }

  try {
    const response = await fetch(`${BASE_URL}/api/staff/bookings?route=${invalidRoute}`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json',
      }
    });

    console.log('Invalid Route Response Status:', response.status);
    const result = await response.json();
    console.log('Invalid Route Response:', JSON.stringify(result, null, 2));

    if (response.status === 403) {
      console.log('✅ Invalid route access properly blocked');
      return true;
    } else {
      console.log('❌ Invalid route access not properly blocked');
      return false;
    }
  } catch (error) {
    console.log('❌ Invalid route access test error:', error.message);
    return false;
  }
}

// Main test execution
async function runAllTests() {
  console.log('🚀 Starting comprehensive staff API tests...\n');

  // Test 1: Staff Login
  const token = await testStaffLogin();
  
  // Test 2: Session Validation
  const staffData = await testStaffValidation(token);
  
  // Test 3: Bookings API
  if (staffData) {
    await testStaffBookingsAPI(token, staffData.route_code);
    
    // Test 4: Historical Bookings API
    await testStaffHistoricalBookingsAPI(token, staffData.route_code);
    
    // Test 6: Invalid Route Access
    await testInvalidRouteAccess(token, 'invalid-route');
  }
  
  // Test 5: Unauthenticated Access
  await testUnauthenticatedAccess();

  console.log('\n🎯 Test Summary:');
  console.log('Check the console output above for detailed results.');
  console.log('Look for ❌ errors and ✅ successes to identify issues.');
}

// Run the tests
runAllTests().catch(console.error); 
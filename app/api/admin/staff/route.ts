export const dynamic = "force-dynamic";

import { NextRequest, NextResponse } from 'next/server';
import { withAdminAuth, getUser } from '@/lib/middleware';
import { hashPassword } from '@/lib/auth';
import { supabaseAdmin } from '@/lib/supabase';
import { createApiResponse, handleApiError, validateRequestBody } from '@/lib/middleware';

// GET - Fetch all staff users
export async function GET(request: NextRequest) {
  return withAdminAuth(request, async (req) => {
    try {
      const user = getUser(req);
      if (!user) {
        return NextResponse.json(
          { error: 'Admin authentication required' },
          { status: 401 }
        );
      }

      // Fetch all staff users
      const { data: staffUsers, error } = await supabaseAdmin
        .from('staff_users')
        .select('id, username, route_code, created_at, updated_at')
        .order('created_at', { ascending: false });

      if (error) {
        console.error('Error fetching staff users:', error);
        return NextResponse.json(
          { error: 'Failed to fetch staff users' },
          { status: 500 }
        );
      }

      return NextResponse.json(
        createApiResponse(staffUsers || [])
      );
    } catch (error) {
      return handleApiError(error, 'Failed to fetch staff users');
    }
  });
}

// POST - Create new staff user
export async function POST(request: NextRequest) {
  return withAdminAuth(request, async (req) => {
    try {
      const user = getUser(req);
      if (!user) {
        return NextResponse.json(
          { error: 'Admin authentication required' },
          { status: 401 }
        );
      }

      const body = await request.json();

      // Validate request body
      const validation = validateRequestBody<{
        username: string;
        password: string;
        route_code: string;
      }>(body, ['username', 'password', 'route_code']);

      if (!validation.isValid) {
        return NextResponse.json(
          { error: 'Validation failed', details: validation.errors },
          { status: 400 }
        );
      }

      const { username, password, route_code } = body;

      // Check if username already exists
      const { data: existingUser } = await supabaseAdmin
        .from('staff_users')
        .select('id')
        .eq('username', username)
        .single();

      if (existingUser) {
        return NextResponse.json(
          { error: 'Username already exists' },
          { status: 400 }
        );
      }

      // Verify route_code exists in buses table
      const { data: bus } = await supabaseAdmin
        .from('buses')
        .select('route_code')
        .eq('route_code', route_code)
        .single();

      if (!bus) {
        return NextResponse.json(
          { error: 'Invalid route code' },
          { status: 400 }
        );
      }

      // Hash password
      const passwordHash = await hashPassword(password);

      // Create staff user
      const { data: newStaff, error: createError } = await supabaseAdmin
        .from('staff_users')
        .insert({
          username,
          password_hash: passwordHash,
          route_code
        })
        .select('id, username, route_code, created_at, updated_at')
        .single();

      if (createError) {
        console.error('Error creating staff user:', createError);
        return NextResponse.json(
          { error: 'Failed to create staff user' },
          { status: 500 }
        );
      }

      return NextResponse.json(
        createApiResponse(newStaff),
        { status: 201 }
      );
    } catch (error) {
      return handleApiError(error, 'Failed to create staff user');
    }
  });
}

// Debug script to check staff database and authentication
console.log('🔍 Debugging Staff Database and Authentication...\n');

const { createClient } = require('@supabase/supabase-js');

// Initialize Supabase client (you'll need to set these environment variables)
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || 'http://localhost:54321';
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY || 'your-service-role-key';

console.log('Supabase URL:', supabaseUrl);
console.log('Service Key:', supabaseServiceKey ? 'Set' : 'Not Set');

if (!supabaseServiceKey || supabaseServiceKey === 'your-service-role-key') {
  console.log('❌ Please set SUPABASE_SERVICE_ROLE_KEY environment variable');
  console.log('Or update this script with your actual service role key');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function checkStaffDatabase() {
  try {
    console.log('1. Checking if staff_users table exists...');
    
    // Check if staff_users table exists
    const { data: tables, error: tablesError } = await supabase
      .from('information_schema.tables')
      .select('table_name')
      .eq('table_schema', 'public')
      .eq('table_name', 'staff_users');

    if (tablesError) {
      console.log('❌ Error checking tables:', tablesError);
      return;
    }

    if (tables.length === 0) {
      console.log('❌ staff_users table does not exist');
      return;
    }

    console.log('✅ staff_users table exists');

    // Check staff_users data
    console.log('\n2. Checking staff_users data...');
    const { data: staffUsers, error: staffError } = await supabase
      .from('staff_users')
      .select('*');

    if (staffError) {
      console.log('❌ Error fetching staff users:', staffError);
      return;
    }

    if (staffUsers.length === 0) {
      console.log('❌ No staff users found in database');
      return;
    }

    console.log(`✅ Found ${staffUsers.length} staff users:`);
    staffUsers.forEach((user, index) => {
      console.log(`  ${index + 1}. Username: ${user.username}, Route: ${user.route_code}, ID: ${user.id}`);
      console.log(`     Password hash: ${user.password_hash.substring(0, 20)}...`);
    });

    // Check buses table for route codes
    console.log('\n3. Checking buses table for route codes...');
    const { data: buses, error: busesError } = await supabase
      .from('buses')
      .select('route_code, name');

    if (busesError) {
      console.log('❌ Error fetching buses:', busesError);
      return;
    }

    if (buses.length === 0) {
      console.log('❌ No buses found in database');
      return;
    }

    console.log(`✅ Found ${buses.length} buses:`);
    buses.forEach((bus, index) => {
      console.log(`  ${index + 1}. Route: ${bus.route_code}, Name: ${bus.name}`);
    });

    // Check current_bookings table
    console.log('\n4. Checking current_bookings table...');
    const { data: currentBookings, error: bookingsError } = await supabase
      .from('current_bookings')
      .select('count');

    if (bookingsError) {
      console.log('❌ Error fetching current_bookings:', bookingsError);
      return;
    }

    console.log(`✅ current_bookings table exists with ${currentBookings.length} records`);

    // Check if there are any bookings for the staff routes
    const staffRoutes = staffUsers.map(user => user.route_code);
    console.log('\n5. Checking for bookings in staff routes:', staffRoutes);
    
    const { data: routeBookings, error: routeBookingsError } = await supabase
      .from('current_bookings')
      .select('bus_route, count')
      .in('bus_route', staffRoutes);

    if (routeBookingsError) {
      console.log('❌ Error fetching route bookings:', routeBookingsError);
      return;
    }

    if (routeBookings.length === 0) {
      console.log('❌ No bookings found for staff routes');
    } else {
      console.log(`✅ Found bookings for staff routes:`, routeBookings);
    }

  } catch (error) {
    console.log('❌ Database check error:', error);
  }
}

async function testPasswordHash() {
  try {
    console.log('\n6. Testing password hash verification...');
    
    // Test with the known password 'staff123'
    const testPassword = 'staff123';
    const testHash = '$2b$10$K8BEyIXM2wMvWO7YdlzOOeEHrZxmBdZVXseVMvvJ6.H0kUjYVUjYm';
    
    // We need bcrypt to test this, but for now let's just show the hash
    console.log(`Test password: ${testPassword}`);
    console.log(`Test hash: ${testHash}`);
    console.log('Note: To verify this hash, you need bcrypt installed');
    
  } catch (error) {
    console.log('❌ Password hash test error:', error);
  }
}

async function runDatabaseChecks() {
  console.log('🚀 Starting database checks...\n');
  
  await checkStaffDatabase();
  await testPasswordHash();
  
  console.log('\n🎯 Database Check Summary:');
  console.log('Check the output above for any ❌ errors or ✅ successes.');
  console.log('If staff_users table is empty, you may need to run migrations.');
}

// Run the checks
runDatabaseChecks().catch(console.error); 
/*
  # Add Staff Route Unique Constraint
  
  This migration enforces a one-to-one relationship between staff users and bus routes
  by adding a unique constraint on route_code in the staff_users table.
  
  This ensures that each bus route can only be assigned to one staff member at a time,
  preventing conflicts and maintaining clear accountability for route management.
*/

-- Add unique constraint on route_code to ensure one staff member per route
ALTER TABLE staff_users 
ADD CONSTRAINT unique_staff_route_code UNIQUE (route_code);

-- Create a function to check if a route is available for assignment
CREATE OR REPLACE FUNCTION is_route_available_for_staff(
  p_route_code VARCHAR(50),
  p_exclude_staff_id INTEGER DEFAULT NULL
)
RETURNS BOOLEAN AS $$
BEGIN
  -- Check if route exists in buses table
  IF NOT EXISTS (SELECT 1 FROM buses WHERE route_code = p_route_code) THEN
    RETURN FALSE;
  END IF;
  
  -- Check if route is already assigned to another staff member
  IF p_exclude_staff_id IS NULL THEN
    -- For new staff creation
    RETURN NOT EXISTS (SELECT 1 FROM staff_users WHERE route_code = p_route_code);
  ELSE
    -- For staff updates, exclude the current staff member
    RETURN NOT EXISTS (
      SELECT 1 FROM staff_users 
      WHERE route_code = p_route_code AND id != p_exclude_staff_id
    );
  END IF;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create a function to get available routes for staff assignment
CREATE OR REPLACE FUNCTION get_available_routes_for_staff(
  p_exclude_staff_id INTEGER DEFAULT NULL
)
RETURNS TABLE(route_code VARCHAR(50), name VARCHAR(100)) AS $$
BEGIN
  RETURN QUERY
  SELECT b.route_code, b.name
  FROM buses b
  WHERE b.is_active = true
    AND NOT EXISTS (
      SELECT 1 FROM staff_users s 
      WHERE s.route_code = b.route_code 
        AND (p_exclude_staff_id IS NULL OR s.id != p_exclude_staff_id)
    )
  ORDER BY b.route_code;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create a function to get route assignment status
CREATE OR REPLACE FUNCTION get_route_assignment_status()
RETURNS TABLE(
  route_code VARCHAR(50),
  route_name VARCHAR(100),
  is_assigned BOOLEAN,
  assigned_staff_username VARCHAR(50),
  assigned_staff_id INTEGER
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    b.route_code,
    b.name as route_name,
    CASE WHEN s.id IS NOT NULL THEN true ELSE false END as is_assigned,
    s.username as assigned_staff_username,
    s.id as assigned_staff_id
  FROM buses b
  LEFT JOIN staff_users s ON b.route_code = s.route_code
  WHERE b.is_active = true
  ORDER BY b.route_code;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant permissions on the new functions
GRANT EXECUTE ON FUNCTION is_route_available_for_staff(VARCHAR(50), INTEGER) TO authenticated;
GRANT EXECUTE ON FUNCTION get_available_routes_for_staff(INTEGER) TO authenticated;
GRANT EXECUTE ON FUNCTION get_route_assignment_status() TO authenticated; 
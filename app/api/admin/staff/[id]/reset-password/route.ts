export const dynamic = "force-dynamic";

import { NextRequest, NextResponse } from 'next/server';
import { withAdminAuth, getUser } from '@/lib/middleware';
import { hashPassword } from '@/lib/auth';
import { supabaseAdmin } from '@/lib/supabase';
import { createApiResponse, handleApiError, validateRequestBody } from '@/lib/middleware';

// POST - Reset staff user password
export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  return withAdminAuth(request, async (req) => {
    try {
      const user = getUser(req);
      if (!user) {
        return NextResponse.json(
          { error: 'Admin authentication required' },
          { status: 401 }
        );
      }

      const staffId = parseInt(params.id);
      if (isNaN(staffId)) {
        return NextResponse.json(
          { error: 'Invalid staff ID' },
          { status: 400 }
        );
      }

      const body = await request.json();

      // Validate request body
      const validation = validateRequestBody<{
        password: string;
      }>(body, ['password']);

      if (!validation.isValid) {
        return NextResponse.json(
          { error: 'Validation failed', details: validation.errors },
          { status: 400 }
        );
      }

      const { password } = body;

      // Validate password length
      if (password.length < 6) {
        return NextResponse.json(
          { error: 'Password must be at least 6 characters long' },
          { status: 400 }
        );
      }

      // Check if staff user exists
      const { data: existingStaff } = await supabaseAdmin
        .from('staff_users')
        .select('id, username')
        .eq('id', staffId)
        .single();

      if (!existingStaff) {
        return NextResponse.json(
          { error: 'Staff user not found' },
          { status: 404 }
        );
      }

      // Hash new password
      const passwordHash = await hashPassword(password);

      // Update staff user password
      const { error: updateError } = await supabaseAdmin
        .from('staff_users')
        .update({
          password_hash: passwordHash,
          updated_at: new Date().toISOString()
        })
        .eq('id', staffId);

      if (updateError) {
        console.error('Error updating staff password:', updateError);
        return NextResponse.json(
          { error: 'Failed to reset password' },
          { status: 500 }
        );
      }

      // Invalidate all existing sessions for this staff user
      const { error: sessionError } = await supabaseAdmin
        .from('staff_sessions')
        .delete()
        .eq('staff_id', staffId);

      if (sessionError) {
        console.error('Error invalidating staff sessions:', sessionError);
        // Don't fail the request if session cleanup fails
      }

      return NextResponse.json(
        createApiResponse({
          message: 'Password reset successfully',
          username: existingStaff.username
        })
      );
    } catch (error) {
      return handleApiError(error, 'Failed to reset staff password');
    }
  });
}

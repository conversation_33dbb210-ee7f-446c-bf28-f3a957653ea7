'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import { PageTransition } from '@/components/ui/page-transition';
import { useStaff, withStaffAuth } from '@/contexts/StaffContext';
import { LogOut, Users, Bus, Calendar, RefreshCw, History } from 'lucide-react';
import { motion } from 'framer-motion';
import { toast } from 'sonner';

interface Booking {
  id: number;
  admission_number: string;
  student_name: string;
  bus_route: string;
  destination: string;
  payment_status: boolean;
  created_at: string;
  go_date: string | null;
  return_date: string | null;
  fare: number | null;
  bus_name: string;
}

interface BookingStats {
  total: number;
}

function StaffDashboard() {
  const { staff, logout } = useStaff();
  const [bookings, setBookings] = useState<Booking[]>([]);
  const [stats, setStats] = useState<BookingStats>({ total: 0 });
  const [busName, setBusName] = useState<string>('Unknown Bus');
  const [isLoading, setIsLoading] = useState(true);
  const router = useRouter();

  const fetchBookings = async () => {
    try {
      setIsLoading(true);
      const response = await fetch(`/api/staff/bookings?route=${staff?.route_code}`, {
        credentials: 'include'
      });

      if (response.ok) {
        const result = await response.json();
        console.log('API Response:', result); // Debug log
        
        if (result.success) {
          // Fix: Access the nested data structure correctly
          setBookings(result.data.bookings || []);
          setStats(result.data.stats || { total: 0 });
          setBusName(result.data.bus_name || 'Unknown Bus');
        } else {
          console.error('API returned success: false:', result);
          toast.error(result.error || 'Failed to fetch bookings');
        }
      } else {
        const errorData = await response.json().catch(() => ({}));
        console.error('API error response:', errorData);
        toast.error(errorData.error || 'Failed to fetch bookings');
      }
    } catch (error) {
      console.error('Error fetching bookings:', error);
      toast.error('Failed to fetch bookings');
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    if (staff?.route_code) {
      fetchBookings();
    }
  }, [staff?.route_code]);

  const handleLogout = async () => {
    await logout();
  };

  const handleViewHistoricalBookings = () => {
    router.push('/staff/historical-bookings');
  };

  const formatDate = (dateString: string | null) => {
    if (!dateString) return 'N/A';
    return new Date(dateString).toLocaleDateString('en-IN', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  const formatCurrency = (amount: number | null) => {
    if (amount === null) return 'N/A';
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR'
    }).format(amount);
  };

  return (
    <PageTransition>
      <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-green-50">
        {/* Header */}
        <div className="bg-white shadow-sm border-b">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="flex justify-between items-center py-4">
              <div className="flex items-center space-x-4">
                <div className="w-10 h-10 bg-gradient-to-r from-blue-600 to-green-600 rounded-full flex items-center justify-center">
                  <Users className="w-6 h-6 text-white" />
                </div>
                <div>
                  <h1 className="text-2xl font-bold text-gray-900">Staff Dashboard</h1>
                  <p className="text-gray-600">Welcome, {staff?.username}</p>
                </div>
              </div>
              <div className="flex items-center space-x-4">
                <Badge variant="outline" className="px-3 py-1">
                  <Bus className="w-4 h-4 mr-2" />
                  Route: {staff?.route_code} - {busName}
                </Badge>
                <Button
                  onClick={handleViewHistoricalBookings}
                  variant="outline"
                  size="sm"
                  className="bg-blue-50 text-blue-700 hover:bg-blue-100"
                >
                  <History className="w-4 h-4 mr-2" />
                  All Time {staff?.route_code} Bookings
                </Button>
                <Button
                  onClick={fetchBookings}
                  variant="outline"
                  size="sm"
                  disabled={isLoading}
                >
                  <RefreshCw className={`w-4 h-4 mr-2 ${isLoading ? 'animate-spin' : ''}`} />
                  Refresh
                </Button>
                <Button
                  onClick={handleLogout}
                  variant="outline"
                  size="sm"
                  className="text-red-600 hover:text-red-700 hover:bg-red-50"
                >
                  <LogOut className="w-4 h-4 mr-2" />
                  Logout
                </Button>
              </div>
            </div>
          </div>
        </div>

        {/* Main Content */}
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          {/* Stats Cards - Simplified to only Total Bookings */}
          <div className="grid grid-cols-1 md:grid-cols-1 gap-6 mb-8">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.1 }}
            >
              <Card>
                <CardContent className="p-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-gray-600">Total Bookings</p>
                      <p className="text-3xl font-bold text-gray-900">{stats.total}</p>
                    </div>
                    <Users className="w-8 h-8 text-blue-600" />
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          </div>

          {/* Current Bookings Table */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.2 }}
          >
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Bus className="w-5 h-5" />
                  Current Route Bookings - {staff?.route_code}
                </CardTitle>
              </CardHeader>
              <CardContent>
                {isLoading ? (
                  <div className="flex items-center justify-center py-8">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
                  </div>
                ) : bookings.length === 0 ? (
                  <div className="text-center py-8 text-gray-500">
                    No current bookings found for this route.
                  </div>
                ) : (
                  <div className="overflow-x-auto">
                    <Table>
                      <TableHeader>
                        <TableRow>
                          <TableHead>Admission No.</TableHead>
                          <TableHead>Student Name</TableHead>
                          <TableHead>Destination</TableHead>
                          <TableHead>Go Date</TableHead>
                          <TableHead>Return Date</TableHead>
                          <TableHead>Fare</TableHead>
                          <TableHead>Payment Status</TableHead>
                          <TableHead>Booking Date</TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {bookings.map((booking) => (
                          <TableRow key={booking.id}>
                            <TableCell className="font-medium">{booking.admission_number}</TableCell>
                            <TableCell>{booking.student_name}</TableCell>
                            <TableCell>{booking.destination}</TableCell>
                            <TableCell>{formatDate(booking.go_date)}</TableCell>
                            <TableCell>{formatDate(booking.return_date)}</TableCell>
                            <TableCell>{formatCurrency(booking.fare)}</TableCell>
                            <TableCell>
                              <Badge variant={booking.payment_status ? "default" : "secondary"}>
                                {booking.payment_status ? 'Paid' : 'Unpaid'}
                              </Badge>
                            </TableCell>
                            <TableCell>{formatDate(booking.created_at)}</TableCell>
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                  </div>
                )}
              </CardContent>
            </Card>
          </motion.div>
        </div>
      </div>
    </PageTransition>
  );
}

export default withStaffAuth(StaffDashboard);

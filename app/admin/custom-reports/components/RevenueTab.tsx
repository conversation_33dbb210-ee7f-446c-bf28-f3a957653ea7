'use client';

import React, { useState, useEffect, useCallback } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { Download, TrendingUp, DollarSign, BarChart3 } from 'lucide-react';
import { toast } from 'sonner';

interface DateRange {
  startDate: string;
  endDate: string;
}

interface RouteRevenue {
  busRoute: string;
  busName: string;
  totalRevenue: number;
  bookingCount: number;
  revenuePerBooking: number;
  percentageOfTotal: number;
}

interface DailyRevenue {
  date: string;
  revenue: number;
}

interface RevenueData {
  totalRevenue: number;
  totalBookings: number;
  averageRevenuePerBooking: number;
  routes: RouteRevenue[];
  dailyBreakdown: DailyRevenue[];
  summary: {
    dateRange: {
      startDate: string;
      endDate: string;
    };
    totalRoutes: number;
    highestRevenueRoute: RouteRevenue | null;
    lowestRevenueRoute: RouteRevenue | null;
  };
}

interface RevenueTabProps {
  dateRange: DateRange;
  isRefreshing: boolean;
}

export default function RevenueTab({ dateRange, isRefreshing }: RevenueTabProps) {
  const [data, setData] = useState<RevenueData | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchRevenueData = useCallback(async () => {
    setIsLoading(true);
    setError(null);

    try {
      const params = new URLSearchParams({
        start_date: dateRange.startDate,
        end_date: dateRange.endDate
      });

      const response = await fetch(`/api/admin/custom-reports/revenue?${params}`, {
        credentials: 'include'
      });

      const result = await response.json();

      if (result.success) {
        setData(result.data);
      } else {
        setError(result.error || 'Failed to fetch revenue data');
        toast.error('Failed to fetch revenue data');
      }
    } catch (error) {
      setError('Network error while fetching revenue data');
      toast.error('Network error while fetching revenue data');
      console.error('Revenue fetch error:', error);
    } finally {
      setIsLoading(false);
    }
  }, [dateRange]);

  useEffect(() => {
    fetchRevenueData();
  }, [fetchRevenueData]);

  const handleExportRevenue = () => {
    if (!data) return;
    
    const headers = ['Bus Route', 'Bus Name', 'Total Revenue', 'Booking Count', 'Revenue Per Booking', 'Percentage of Total'];
    const csvContent = [
      headers.join(','),
      ...data.routes.map(route => [
        route.busRoute,
        `"${route.busName}"`,
        route.totalRevenue,
        route.bookingCount,
        route.revenuePerBooking.toFixed(2),
        route.percentageOfTotal.toFixed(2) + '%'
      ].join(','))
    ].join('\n');

    const blob = new Blob([csvContent], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `revenue-report-${dateRange.startDate}-to-${dateRange.endDate}.csv`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    window.URL.revokeObjectURL(url);
    
    toast.success('Revenue data exported successfully');
  };

  const handleExportDaily = () => {
    if (!data) return;
    
    const headers = ['Date', 'Revenue'];
    const csvContent = [
      headers.join(','),
      ...data.dailyBreakdown.map(day => [
        day.date,
        day.revenue
      ].join(','))
    ].join('\n');

    const blob = new Blob([csvContent], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `daily-revenue-${dateRange.startDate}-to-${dateRange.endDate}.csv`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    window.URL.revokeObjectURL(url);
    
    toast.success('Daily revenue data exported successfully');
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center py-12">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-green-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading revenue data...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="text-center py-12">
        <p className="text-red-600 mb-4">{error}</p>
        <Button onClick={fetchRevenueData} variant="outline">
          Try Again
        </Button>
      </div>
    );
  }

  if (!data) {
    return (
      <div className="text-center py-12">
        <p className="text-gray-600">No revenue data available</p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <div>
                <p className="text-sm font-medium text-gray-600">Total Revenue</p>
                <p className="text-2xl font-bold text-green-600">₹{data.totalRevenue.toLocaleString()}</p>
              </div>
              <DollarSign className="h-8 w-8 text-green-600 ml-auto" />
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <div>
                <p className="text-sm font-medium text-gray-600">Total Bookings</p>
                <p className="text-2xl font-bold text-blue-600">{data.totalBookings}</p>
              </div>
              <BarChart3 className="h-8 w-8 text-blue-600 ml-auto" />
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <div>
                <p className="text-sm font-medium text-gray-600">Avg Revenue/Booking</p>
                <p className="text-2xl font-bold text-purple-600">₹{data.averageRevenuePerBooking.toFixed(2)}</p>
              </div>
              <TrendingUp className="h-8 w-8 text-purple-600 ml-auto" />
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <div>
                <p className="text-sm font-medium text-gray-600">Active Routes</p>
                <p className="text-2xl font-bold text-orange-600">{data.summary.totalRoutes}</p>
              </div>
              <BarChart3 className="h-8 w-8 text-orange-600 ml-auto" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Top Performing Routes */}
      {data.summary.highestRevenueRoute && (
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <Card>
            <CardHeader>
              <CardTitle className="text-green-600">Highest Revenue Route</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                <p className="font-semibold">{data.summary.highestRevenueRoute.busName}</p>
                <p className="text-sm text-gray-600">Route: {data.summary.highestRevenueRoute.busRoute}</p>
                <p className="text-lg font-bold text-green-600">₹{data.summary.highestRevenueRoute.totalRevenue.toLocaleString()}</p>
                <p className="text-sm text-gray-600">{data.summary.highestRevenueRoute.bookingCount} bookings</p>
              </div>
            </CardContent>
          </Card>
          {data.summary.lowestRevenueRoute && (
            <Card>
              <CardHeader>
                <CardTitle className="text-orange-600">Lowest Revenue Route</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  <p className="font-semibold">{data.summary.lowestRevenueRoute.busName}</p>
                  <p className="text-sm text-gray-600">Route: {data.summary.lowestRevenueRoute.busRoute}</p>
                  <p className="text-lg font-bold text-orange-600">₹{data.summary.lowestRevenueRoute.totalRevenue.toLocaleString()}</p>
                  <p className="text-sm text-gray-600">{data.summary.lowestRevenueRoute.bookingCount} bookings</p>
                </div>
              </CardContent>
            </Card>
          )}
        </div>
      )}

      {/* Route-wise Revenue Table */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            Route-wise Revenue Breakdown
            <Button onClick={handleExportRevenue} variant="outline" size="sm">
              <Download className="w-4 h-4 mr-2" />
              Export Routes
            </Button>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="overflow-x-auto">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Route</TableHead>
                  <TableHead>Bus Name</TableHead>
                  <TableHead>Revenue</TableHead>
                  <TableHead>Bookings</TableHead>
                  <TableHead>Avg/Booking</TableHead>
                  <TableHead>% of Total</TableHead>
                  <TableHead>Performance</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {data.routes.length > 0 ? (
                  data.routes.map((route, index) => (
                    <TableRow key={route.busRoute}>
                      <TableCell className="font-medium">{route.busRoute}</TableCell>
                      <TableCell>{route.busName}</TableCell>
                      <TableCell className="font-semibold text-green-600">₹{route.totalRevenue.toLocaleString()}</TableCell>
                      <TableCell>{route.bookingCount}</TableCell>
                      <TableCell>₹{route.revenuePerBooking.toFixed(2)}</TableCell>
                      <TableCell>{route.percentageOfTotal.toFixed(1)}%</TableCell>
                      <TableCell>
                        <div className="w-full">
                          <Progress value={route.percentageOfTotal} className="h-2" />
                        </div>
                      </TableCell>
                    </TableRow>
                  ))
                ) : (
                  <TableRow>
                    <TableCell colSpan={7} className="text-center py-8 text-gray-500">
                      No revenue data found for the selected date range
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
          </div>
        </CardContent>
      </Card>

      {/* Daily Revenue Breakdown */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            Daily Revenue Breakdown
            <Button onClick={handleExportDaily} variant="outline" size="sm">
              <Download className="w-4 h-4 mr-2" />
              Export Daily
            </Button>
          </CardTitle>
        </CardHeader>
        <CardContent>
          {data.dailyBreakdown.length > 0 ? (
            <div className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {data.dailyBreakdown.map((day) => (
                  <div key={day.date} className="p-4 border rounded-lg">
                    <div className="flex justify-between items-center">
                      <span className="text-sm font-medium text-gray-600">
                        {new Date(day.date).toLocaleDateString()}
                      </span>
                      <span className="text-lg font-bold text-green-600">
                        ₹{day.revenue.toLocaleString()}
                      </span>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          ) : (
            <div className="text-center py-8 text-gray-500">
              No daily revenue data available for the selected date range
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
